<template>
  <div style="display: flex; flex-direction: column; height: 100dvh">
    <AppHeader style="flex: 0" />
    <router-view style="flex: 1" />
  </div>
</template>

<script lang="ts" setup>
import AppHeader from "@/components/AppHeader.vue";
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body {
  margin: 0;
}

</style>
