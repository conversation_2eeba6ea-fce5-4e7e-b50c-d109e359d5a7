/* 整体布局 */
.chat-view {
  display: grid;
  grid-template-columns: 1fr 5fr;
}
.chat-view h3 {
  margin-block: 0 0.6dvw;
  font-size: 0.9dvw;
}

/* 左侧布局 */
.chat-slider {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  color: white;
}
.chat-function {
  flex: 0 0 auto;
  padding: 1.2dvw 1dvw;
}
.chat-history {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0 0.1dvw 1.2dvw 1dvw;
}
.chat-function-list {
  list-style: none;
  padding-left: 0;
  margin: 0;
}
.chat-function-list li {
  padding: 0.5dvw;
  font-size: 0.9dvw;
  line-height: 1dvw;
  margin-bottom: 0.5dvw;
  display: flex;
  align-items: center;
  gap: 0.6dvw;
  border-radius: 0.4dvw;
  transition:
    background-color 0.2s ease,
    box-shadow 0.2s ease;
}
.chat-function-list li:last-child {
  margin-bottom: 0;
}
.chat-function-list li:hover {
  background-color: rgba(255, 255, 255, 0.12);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
}
.chat-history-list {
  flex: 1 1 0;
  min-height: 0;
  list-style: none;
  padding-left: 0;
  margin: 0;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(236, 179, 255, 0.4) transparent;
  padding-right: 0.5dvw;
}
.chat-history-list li {
  padding: 0.3dvw;
  font-size: 0.8dvw;
  line-height: 0.9dvw;
  margin-bottom: 0.4dvw;
  border: 0.15dvw solid transparent;
  background-origin: border-box;
  background-clip: padding-box, border-box;
  box-sizing: border-box;
}
.chat-history-list li:hover {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border: 0.15dvw solid rgba(255, 255, 255, 0.18);
  border-radius: 0.4dvw;
  background-color: rgba(255, 255, 255, 0.12);
  background-origin: border-box;
  background-clip: padding-box, border-box;
  cursor: pointer;
}
.chat-history-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-history-title > i {
  margin-bottom: 0.6dvw;
  margin-right: 1dvw;
  padding: 0.3dvw;
  border-radius: 0.4dvw;
  border: 0.15dvw solid transparent;
  transition:
    background-color 0.2s ease,
    box-shadow 0.2s ease,
    border-color 0.2s ease;
}

.chat-history-title > i:hover {
  background-color: rgba(255, 255, 255, 0.12);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.18);
  cursor: pointer;
}

/* 欢迎界面 */
.chat-welcome {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
}
.welcome-logo {
  width: 8dvw;
  height: 8dvw;
  margin-bottom: 2dvw;
  opacity: 0.9;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
}
.welcome-title {
  font-size: 1.8dvw;
  font-weight: 600;
  margin: 0 0 1dvw 0;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}
.welcome-subtitle {
  font-size: 1dvw;
  margin: 0;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

/* 右侧布局 */
.chat-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: white;
}
.chat-content h3 {
  margin-bottom: 0;
  margin-right: 0.2dvw;
}
.chat-content-header {
  padding: 1.2dvw 1dvw;
  flex: 0;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}
.chat-content-header-subtitle{
  font-size: 0.8dvw;
}
.new-chat-button{
  font-size: 0.8dvw;
  position: absolute;
  right: 2dvw;
  top: 50%;
  transform: translateY(-50%);
  padding: 0.35dvw 1dvw;
  color: #ffffff;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.16), rgba(255, 255, 255, 0.08));
  border: 0.15dvw solid rgba(255, 255, 255, 0.45);
  border-radius: 0.5dvw;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.35);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.25),
    0 0 12px rgba(255, 255, 255, 0.18),
    inset 0 1px 3px rgba(255, 255, 255, 0.18);
  cursor: pointer;
  white-space: nowrap;
  line-height: 1;
  user-select: none;
  font-weight: bold;
  letter-spacing: 0.05dvw;
  transition:
    background-color 0.2s ease,
    box-shadow 0.2s ease,
    border-color 0.2s ease,
    color 0.2s ease,
    transform 0.12s ease;
}
.new-chat-button:hover{
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.24), rgba(255, 255, 255, 0.14));
  border-color: rgba(255, 255, 255, 0.65);
  box-shadow:
    0 4px 14px rgba(0, 0, 0, 0.28),
    0 0 16px rgba(255, 255, 255, 0.28),
    inset 0 1px 3px rgba(255, 255, 255, 0.25);
  transform: translateY(-50%);
}
.new-chat-button:active{
  transform: translateY(-50%) scale(0.97);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.25),
    0 0 10px rgba(255, 255, 255, 0.22),
    inset 0 1px 3px rgba(255, 255, 255, 0.15);
}
.new-chat-button:focus-visible{
  outline: none;
  border-color: rgba(255, 255, 255, 0.85);
  box-shadow:
    0 0 0 0.2dvw rgba(255, 255, 255, 0.35),
    0 4px 14px rgba(255, 255, 255, 0.3);
}

.chat-content-main {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 1.2dvw 20dvw;
}
